<!-- Enhanced Hero Section -->
<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-accent-600 dark:from-primary-800 dark:via-primary-900 dark:to-accent-800 text-white py-20 lg:py-32 transition-all duration-500 overflow-hidden">
  <!-- Background Effects -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-br from-primary-800/20 via-transparent to-accent-800/20"></div>
    <div class="absolute top-20 left-20 w-64 h-64 bg-primary-400/10 rounded-full blur-3xl animate-pulse-slow"></div>
    <div class="absolute bottom-20 right-20 w-80 h-80 bg-accent-400/10 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 1s;"></div>
    <!-- Floating Particles -->
    <div class="absolute top-32 left-32 w-3 h-3 bg-accent-400 rounded-full opacity-60 animate-float"></div>
    <div class="absolute top-64 right-48 w-2 h-2 bg-primary-300 rounded-full opacity-40 animate-float" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-48 left-48 w-4 h-4 bg-white rounded-full opacity-30 animate-float" style="animation-delay: 2s;"></div>
    <div class="absolute bottom-32 right-32 w-2 h-2 bg-accent-300 rounded-full opacity-50 animate-float" style="animation-delay: 3s;"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="text-center animate-fade-in-up">
      <!-- Enhanced Badge -->
      <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-accent-300 text-sm font-medium mb-8 border border-white/20">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
        </svg>
        Get In Touch
      </div>

      <!-- Enhanced Heading -->
      <h1 class="text-5xl lg:text-7xl font-bold mb-8 leading-tight">
        <span class="block text-white mb-2">Contact</span>
        <span class="block text-gradient bg-gradient-to-r from-accent-300 via-accent-200 to-white bg-clip-text text-transparent animate-glow">Our Experts</span>
      </h1>

      <!-- Enhanced Subtitle -->
      <p class="text-xl lg:text-2xl text-primary-100 max-w-4xl mx-auto leading-relaxed mb-12">
        Ready to take control of your finances? Get in touch with our expert team for professional consultation and personalized financial solutions.
      </p>

      <!-- Quick Contact Stats -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
        <div class="group flex flex-col items-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300 hover:-translate-y-1">
          <div class="w-12 h-12 bg-gradient-to-br from-accent-400 to-accent-500 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="text-white font-semibold text-sm">24 Hours</div>
          <div class="text-primary-200 text-xs">Response Time</div>
        </div>

        <div class="group flex flex-col items-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300 hover:-translate-y-1">
          <div class="w-12 h-12 bg-gradient-to-br from-primary-400 to-primary-500 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
            </svg>
          </div>
          <div class="text-white font-semibold text-sm">Free</div>
          <div class="text-primary-200 text-xs">Consultation</div>
        </div>

        <div class="group flex flex-col items-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300 hover:-translate-y-1">
          <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="text-white font-semibold text-sm">15+ Years</div>
          <div class="text-primary-200 text-xs">Experience</div>
        </div>

        <div class="group flex flex-col items-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300 hover:-translate-y-1">
          <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-500 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="text-white font-semibold text-sm">500+</div>
          <div class="text-primary-200 text-xs">Happy Clients</div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced Contact Methods Section -->
<section class="section-padding bg-gradient-to-br from-secondary-50 via-white to-primary-50/30 dark:from-secondary-900 dark:via-secondary-800 dark:to-primary-900/30 transition-colors duration-500 relative overflow-hidden">
  <!-- Background Effects -->
  <div class="absolute inset-0 opacity-5 dark:opacity-10">
    <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-100 via-transparent to-accent-100 dark:from-primary-900 dark:to-accent-900"></div>
    <div class="absolute top-20 left-20 w-32 h-32 bg-primary-200 dark:bg-primary-800 rounded-full blur-3xl"></div>
    <div class="absolute bottom-20 right-20 w-40 h-40 bg-accent-200 dark:bg-accent-800 rounded-full blur-3xl"></div>
  </div>

  <div class="container-custom relative z-10">
    <!-- Enhanced Header -->
    <div class="text-center mb-20 animate-fade-in-up">
      <div class="inline-flex items-center px-4 py-2 bg-primary-100 dark:bg-primary-900/30 rounded-full text-primary-600 dark:text-primary-400 text-sm font-medium mb-6 transition-colors duration-300">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        Multiple Ways to Connect
      </div>
      <h2 class="text-4xl lg:text-6xl font-bold text-secondary-800 dark:text-white mb-6 leading-tight transition-colors duration-300">
        Get In <span class="text-gradient bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">Touch</span>
      </h2>
      <p class="text-xl lg:text-2xl text-secondary-600 dark:text-secondary-300 max-w-4xl mx-auto leading-relaxed transition-colors duration-300">
        Choose your preferred way to reach us. We're here to help with all your financial needs and provide expert guidance.
      </p>
    </div>

    <!-- Enhanced Contact Method Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
      <div *ngFor="let method of contactMethods; let i = index"
           class="group relative bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-3xl p-8 border border-secondary-200/50 dark:border-secondary-700/50 hover:shadow-2xl hover:shadow-primary-500/10 dark:hover:shadow-primary-400/10 transition-all duration-500 hover:-translate-y-3 animate-fade-in-up overflow-hidden text-center"
           [style.animation-delay]="(0.1 + i * 0.1) + 's'">

        <!-- Background Gradient -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-50/50 to-accent-50/50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        <!-- Floating Elements -->
        <div class="absolute -top-2 -right-2 w-4 h-4 bg-accent-400 rounded-full opacity-60 animate-float" [style.animation-delay]="(i * 0.5) + 's'"></div>
        <div class="absolute -bottom-2 -left-2 w-3 h-3 bg-primary-400 rounded-full opacity-40 animate-float" [style.animation-delay]="(i * 0.5 + 1) + 's'"></div>

        <div class="relative z-10">
          <!-- Enhanced Icon -->
          <div class="relative mb-8">
            <div class="w-20 h-20 mx-auto bg-gradient-to-br rounded-2xl flex items-center justify-center text-white shadow-2xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 border-4 border-white dark:border-secondary-700"
                 [ngClass]="'bg-gradient-to-br ' + method.color">
              <div [innerHTML]="getIconSvg(method.icon)" class="w-8 h-8 text-white"></div>
            </div>
            <!-- Glow Effect -->
            <div class="absolute inset-0 w-20 h-20 mx-auto bg-gradient-to-br rounded-2xl blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500"
                 [ngClass]="'bg-gradient-to-br ' + method.color"></div>
          </div>

          <!-- Method Info -->
          <div class="space-y-4">
            <h3 class="text-xl font-bold text-secondary-800 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">{{ method.title }}</h3>

            <!-- Contact Value -->
            <a [href]="method.action"
               class="group/link inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary-600 to-accent-600 hover:from-primary-700 hover:to-accent-700 text-white font-medium rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/25 hover:-translate-y-1 text-sm w-full">
              <span>{{ method.value }}</span>
              <svg class="w-4 h-4 ml-2 group-hover/link:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </a>

            <!-- Description -->
            <p class="text-secondary-600 dark:text-secondary-300 text-sm leading-relaxed transition-colors duration-300">{{ method.description }}</p>

            <!-- Availability Badge -->
            <div class="inline-flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium rounded-full border border-green-200 dark:border-green-700">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
              Available Now
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Action Section -->
    <div class="text-center bg-white/50 dark:bg-secondary-800/50 backdrop-blur-sm rounded-3xl p-8 border border-secondary-200/50 dark:border-secondary-700/50">
      <h3 class="text-2xl font-bold text-secondary-800 dark:text-white mb-4 transition-colors duration-300">
        Need Immediate Assistance?
      </h3>
      <p class="text-secondary-600 dark:text-secondary-300 mb-6 transition-colors duration-300">
        For urgent matters, call us directly or schedule a consultation
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="tel:+919876543210" class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold rounded-xl transition-all duration-300 hover:shadow-xl hover:shadow-green-500/25 hover:-translate-y-1 text-lg">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
          </svg>
          <span>Call Now</span>
        </a>
        <a href="https://wa.me/919876543210" class="group inline-flex items-center px-8 py-4 bg-white/80 dark:bg-secondary-700/80 hover:bg-white dark:hover:bg-secondary-600 text-secondary-800 dark:text-white font-semibold rounded-xl border border-secondary-200 dark:border-secondary-600 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 text-lg backdrop-blur-sm">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
          <span>WhatsApp</span>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced Contact Form Section -->
<section class="section-padding bg-gradient-to-br from-white via-primary-50/20 to-accent-50/20 dark:from-secondary-900 dark:via-primary-900/10 dark:to-accent-900/10 transition-colors duration-500 relative overflow-hidden">
  <!-- Background Effects -->
  <div class="absolute inset-0 opacity-5 dark:opacity-10">
    <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-100 via-transparent to-accent-100 dark:from-primary-900 dark:to-accent-900"></div>
    <div class="absolute top-32 left-32 w-48 h-48 bg-primary-200 dark:bg-primary-800 rounded-full blur-3xl"></div>
    <div class="absolute bottom-32 right-32 w-64 h-64 bg-accent-200 dark:bg-accent-800 rounded-full blur-3xl"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
      <!-- Enhanced Contact Form -->
      <div class="animate-fade-in-left">
        <!-- Form Header -->
        <div class="mb-12">
          <div class="inline-flex items-center px-4 py-2 bg-primary-100 dark:bg-primary-900/30 rounded-full text-primary-600 dark:text-primary-400 text-sm font-medium mb-6 transition-colors duration-300">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
            </svg>
            Send Message
          </div>
          <h2 class="text-4xl lg:text-5xl font-bold text-secondary-800 dark:text-white mb-6 leading-tight transition-colors duration-300">
            Let's Start a <span class="text-gradient bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">Conversation</span>
          </h2>
          <p class="text-xl text-secondary-600 dark:text-secondary-300 leading-relaxed transition-colors duration-300">
            Fill out the form below and we'll get back to you within 24 hours with personalized solutions for your financial needs.
          </p>
        </div>

        <!-- Enhanced Success Message -->
        <div *ngIf="submitSuccess"
             class="mb-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 border border-green-200 dark:border-green-700 rounded-2xl animate-fade-in shadow-lg">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h4 class="text-green-800 dark:text-green-200 font-bold text-lg">Message Sent Successfully!</h4>
              <p class="text-green-700 dark:text-green-300">Thank you for reaching out. We'll get back to you within 24 hours.</p>
            </div>
          </div>
        </div>

        <!-- Enhanced Form -->
        <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="space-y-8 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-3xl p-8 border border-secondary-200/50 dark:border-secondary-700/50 shadow-2xl">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Enhanced Name Field -->
            <div class="group animate-fade-in-up" style="animation-delay: 0.1s;">
              <label for="name" class="block text-sm font-semibold text-secondary-700 dark:text-secondary-300 mb-3 transition-colors duration-300 group-focus-within:text-primary-600 dark:group-focus-within:text-primary-400">
                <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
                Full Name *
              </label>
              <div class="relative">
                <input
                  type="text"
                  id="name"
                  formControlName="name"
                  class="w-full px-4 py-4 bg-white/90 dark:bg-secondary-700/90 border border-secondary-300 dark:border-secondary-600 rounded-xl text-secondary-900 dark:text-white placeholder-secondary-500 dark:placeholder-secondary-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 hover:border-primary-400 dark:hover:border-primary-500 backdrop-blur-sm"
                  placeholder="Enter your full name"
                >
                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
              <p *ngIf="getFieldError('name')" class="mt-2 text-sm text-red-600 dark:text-red-400 animate-fade-in flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                {{ getFieldError('name') }}
              </p>
            </div>

            <!-- Enhanced Email Field -->
            <div class="group animate-fade-in-up" style="animation-delay: 0.2s;">
              <label for="email" class="block text-sm font-semibold text-secondary-700 dark:text-secondary-300 mb-3 transition-colors duration-300 group-focus-within:text-primary-600 dark:group-focus-within:text-primary-400">
                <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                </svg>
                Email Address *
              </label>
              <div class="relative">
                <input
                  type="email"
                  id="email"
                  formControlName="email"
                  class="w-full px-4 py-4 bg-white/90 dark:bg-secondary-700/90 border border-secondary-300 dark:border-secondary-600 rounded-xl text-secondary-900 dark:text-white placeholder-secondary-500 dark:placeholder-secondary-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 hover:border-primary-400 dark:hover:border-primary-500 backdrop-blur-sm"
                  placeholder="Enter your email address"
                >
                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
              <p *ngIf="getFieldError('email')" class="mt-2 text-sm text-red-600 dark:text-red-400 animate-fade-in flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                {{ getFieldError('email') }}
              </p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Enhanced Phone Field -->
            <div class="group animate-fade-in-up" style="animation-delay: 0.3s;">
              <label for="phone" class="block text-sm font-semibold text-secondary-700 dark:text-secondary-300 mb-3 transition-colors duration-300 group-focus-within:text-primary-600 dark:group-focus-within:text-primary-400">
                <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                </svg>
                Phone Number *
              </label>
              <div class="relative">
                <input
                  type="tel"
                  id="phone"
                  formControlName="phone"
                  class="w-full px-4 py-4 bg-white/90 dark:bg-secondary-700/90 border border-secondary-300 dark:border-secondary-600 rounded-xl text-secondary-900 dark:text-white placeholder-secondary-500 dark:placeholder-secondary-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 hover:border-primary-400 dark:hover:border-primary-500 backdrop-blur-sm"
                  placeholder="Enter your phone number"
                >
                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
              <p *ngIf="getFieldError('phone')" class="mt-2 text-sm text-red-600 dark:text-red-400 animate-fade-in flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                {{ getFieldError('phone') }}
              </p>
            </div>

            <!-- Enhanced Company Field -->
            <div class="group animate-fade-in-up" style="animation-delay: 0.4s;">
              <label for="company" class="block text-sm font-semibold text-secondary-700 dark:text-secondary-300 mb-3 transition-colors duration-300 group-focus-within:text-primary-600 dark:group-focus-within:text-primary-400">
                <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path>
                </svg>
                Company Name
              </label>
              <div class="relative">
                <input
                  type="text"
                  id="company"
                  formControlName="company"
                  class="w-full px-4 py-4 bg-white/90 dark:bg-secondary-700/90 border border-secondary-300 dark:border-secondary-600 rounded-xl text-secondary-900 dark:text-white placeholder-secondary-500 dark:placeholder-secondary-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 hover:border-primary-400 dark:hover:border-primary-500 backdrop-blur-sm"
                  placeholder="Enter your company name (optional)"
                >
                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>
          </div>

          <!-- Enhanced Service Field -->
          <div class="group animate-fade-in-up" style="animation-delay: 0.5s;">
            <label for="service" class="block text-sm font-semibold text-secondary-700 dark:text-secondary-300 mb-3 transition-colors duration-300 group-focus-within:text-primary-600 dark:group-focus-within:text-primary-400">
              <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
              </svg>
              Service Required *
            </label>
            <div class="relative">
              <select
                id="service"
                formControlName="service"
                class="w-full px-4 py-4 bg-white/90 dark:bg-secondary-700/90 border border-secondary-300 dark:border-secondary-600 rounded-xl text-secondary-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 hover:border-primary-400 dark:hover:border-primary-500 backdrop-blur-sm appearance-none cursor-pointer"
              >
                <option value="">Select a service</option>
                <option value="tax-planning">📊 Tax Planning & Preparation</option>
                <option value="audit">🔍 Audit & Assurance</option>
                <option value="advisory">💼 Business Advisory</option>
                <option value="gst">📋 GST Services</option>
                <option value="registration">🏢 Company Registration</option>
                <option value="financial-planning">📈 Financial Planning</option>
                <option value="other">💬 Other Services</option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                <svg class="w-5 h-5 text-secondary-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
            <p *ngIf="getFieldError('service')" class="mt-2 text-sm text-red-600 dark:text-red-400 animate-fade-in flex items-center">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              {{ getFieldError('service') }}
            </p>
          </div>

          <!-- Enhanced Message Field -->
          <div class="group animate-fade-in-up" style="animation-delay: 0.6s;">
            <label for="message" class="block text-sm font-semibold text-secondary-700 dark:text-secondary-300 mb-3 transition-colors duration-300 group-focus-within:text-primary-600 dark:group-focus-within:text-primary-400">
              <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"></path>
              </svg>
              Message *
            </label>
            <div class="relative">
              <textarea
                id="message"
                formControlName="message"
                rows="6"
                class="w-full px-4 py-4 bg-white/90 dark:bg-secondary-700/90 border border-secondary-300 dark:border-secondary-600 rounded-xl text-secondary-900 dark:text-white placeholder-secondary-500 dark:placeholder-secondary-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 hover:border-primary-400 dark:hover:border-primary-500 backdrop-blur-sm resize-none"
                placeholder="Tell us about your requirements, goals, and how we can help you achieve them..."
              ></textarea>
              <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
            <p *ngIf="getFieldError('message')" class="mt-2 text-sm text-red-600 dark:text-red-400 animate-fade-in flex items-center">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              {{ getFieldError('message') }}
            </p>
          </div>

          <!-- Enhanced Submit Button -->
          <div class="animate-fade-in-up" style="animation-delay: 0.7s;">
            <button
              type="submit"
              [disabled]="isSubmitting"
              class="group relative w-full bg-gradient-to-r from-primary-600 to-accent-600 hover:from-primary-700 hover:to-accent-700 disabled:from-secondary-400 disabled:to-secondary-500 text-white font-bold py-5 px-8 rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-primary-500/25 hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center text-lg overflow-hidden"
            >
              <!-- Background Animation -->
              <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <!-- Loading Spinner -->
              <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-3 h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>

              <!-- Button Content -->
              <span class="relative flex items-center">
                {{ isSubmitting ? 'Sending Message...' : 'Send Message' }}
                <svg *ngIf="!isSubmitting" class="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path>
                </svg>
              </span>
            </button>

            <!-- Form Footer -->
            <div class="mt-6 text-center">
              <p class="text-sm text-secondary-500 dark:text-secondary-400 flex items-center justify-center">
                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                </svg>
                Your information is secure and confidential
              </p>
            </div>
          </div>
        </form>
      </div>

      <!-- Enhanced Contact Information -->
      <div class="animate-fade-in-right">
        <div class="bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-3xl p-8 border border-secondary-200/50 dark:border-secondary-700/50 shadow-2xl h-fit relative overflow-hidden">
          <!-- Background Effects -->
          <div class="absolute inset-0 bg-gradient-to-br from-primary-50/50 to-accent-50/50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-3xl"></div>

          <div class="relative z-10">
            <!-- Header -->
            <div class="mb-8">
              <div class="inline-flex items-center px-4 py-2 bg-primary-100 dark:bg-primary-900/30 rounded-full text-primary-600 dark:text-primary-400 text-sm font-medium mb-4 transition-colors duration-300">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                </svg>
                Contact Details
              </div>
              <h3 class="text-3xl font-bold text-secondary-800 dark:text-white mb-2 transition-colors duration-300">
                Get In Touch <span class="text-gradient bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">Today</span>
              </h3>
              <p class="text-secondary-600 dark:text-secondary-300 transition-colors duration-300">
                Multiple ways to reach our expert team
              </p>
            </div>

            <!-- Contact Info Cards -->
            <div class="space-y-6">
              <!-- Office Address -->
              <div class="group relative bg-white/60 dark:bg-secondary-700/60 backdrop-blur-sm rounded-2xl p-6 border border-secondary-200/50 dark:border-secondary-600/50 hover:shadow-lg hover:shadow-primary-500/10 dark:hover:shadow-primary-400/10 transition-all duration-300 hover:-translate-y-1 animate-fade-in-up overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-primary-50/30 to-accent-50/30 dark:from-primary-900/10 dark:to-accent-900/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div class="relative z-10 flex items-start space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 flex-shrink-0">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h4 class="font-bold text-lg text-secondary-800 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">Office Address</h4>
                    <p class="text-secondary-700 dark:text-secondary-200 font-medium mb-3 transition-colors duration-300">{{ address }}</p>
                    <a href="https://maps.google.com" target="_blank" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-medium rounded-lg transition-all duration-300 hover:shadow-md hover:-translate-y-0.5">
                      <span>View on Map</span>
                      <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                        <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-1a1 1 0 10-2 0v1H5V7h1a1 1 0 000-2H5z"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>

              <!-- Phone Number -->
              <div class="group relative bg-white/60 dark:bg-secondary-700/60 backdrop-blur-sm rounded-2xl p-6 border border-secondary-200/50 dark:border-secondary-600/50 hover:shadow-lg hover:shadow-green-500/10 dark:hover:shadow-green-400/10 transition-all duration-300 hover:-translate-y-1 animate-fade-in-up overflow-hidden" style="animation-delay: 0.1s;">
                <div class="absolute inset-0 bg-gradient-to-br from-green-50/30 to-green-100/30 dark:from-green-900/10 dark:to-green-800/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div class="relative z-10 flex items-start space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 flex-shrink-0">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h4 class="font-bold text-lg text-secondary-800 dark:text-white mb-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">Phone Number</h4>
                    <p class="text-secondary-700 dark:text-secondary-200 font-medium mb-3 transition-colors duration-300">{{ phone }}</p>
                    <a [href]="'tel:' + phone" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white text-sm font-medium rounded-lg transition-all duration-300 hover:shadow-md hover:-translate-y-0.5">
                      <span>Call Now</span>
                      <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>

              <!-- Email Address -->
              <div class="group relative bg-white/60 dark:bg-secondary-700/60 backdrop-blur-sm rounded-2xl p-6 border border-secondary-200/50 dark:border-secondary-600/50 hover:shadow-lg hover:shadow-purple-500/10 dark:hover:shadow-purple-400/10 transition-all duration-300 hover:-translate-y-1 animate-fade-in-up overflow-hidden" style="animation-delay: 0.2s;">
                <div class="absolute inset-0 bg-gradient-to-br from-purple-50/30 to-purple-100/30 dark:from-purple-900/10 dark:to-purple-800/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div class="relative z-10 flex items-start space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 flex-shrink-0">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h4 class="font-bold text-lg text-secondary-800 dark:text-white mb-2 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">Email Address</h4>
                    <p class="text-secondary-700 dark:text-secondary-200 font-medium mb-3 transition-colors duration-300 break-words">{{ email }}</p>
                    <a [href]="'mailto:' + email" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white text-sm font-medium rounded-lg transition-all duration-300 hover:shadow-md hover:-translate-y-0.5">
                      <span>Send Email</span>
                      <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>

              <!-- Business Hours -->
              <div class="group relative bg-white/60 dark:bg-secondary-700/60 backdrop-blur-sm rounded-2xl p-6 border border-secondary-200/50 dark:border-secondary-600/50 hover:shadow-lg hover:shadow-orange-500/10 dark:hover:shadow-orange-400/10 transition-all duration-300 hover:-translate-y-1 animate-fade-in-up overflow-hidden" style="animation-delay: 0.3s;">
                <div class="absolute inset-0 bg-gradient-to-br from-orange-50/30 to-orange-100/30 dark:from-orange-900/10 dark:to-orange-800/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div class="relative z-10 flex items-start space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center text-white shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 flex-shrink-0">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h4 class="font-bold text-lg text-secondary-800 dark:text-white mb-3 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors duration-300">Business Hours</h4>
                    <div class="space-y-2 text-sm">
                      <div class="flex items-center justify-between p-2 bg-white/50 dark:bg-secondary-600/50 rounded-lg">
                        <span class="font-medium text-secondary-700 dark:text-secondary-200">Monday - Friday</span>
                        <span class="text-secondary-600 dark:text-secondary-300">9:00 AM - 6:00 PM</span>
                      </div>
                      <div class="flex items-center justify-between p-2 bg-white/50 dark:bg-secondary-600/50 rounded-lg">
                        <span class="font-medium text-secondary-700 dark:text-secondary-200">Saturday</span>
                        <span class="text-secondary-600 dark:text-secondary-300">9:00 AM - 2:00 PM</span>
                      </div>
                      <div class="flex items-center justify-between p-2 bg-white/50 dark:bg-secondary-600/50 rounded-lg">
                        <span class="font-medium text-secondary-700 dark:text-secondary-200">Sunday</span>
                        <span class="text-red-600 dark:text-red-400 font-medium">Closed</span>
                      </div>
                    </div>
                    <div class="mt-4 inline-flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium rounded-full border border-green-200 dark:border-green-700">
                      <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                      Open Now
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Floating Elements -->
            <div class="absolute -top-4 -right-4 w-20 h-20 bg-accent-400/20 rounded-full opacity-30 animate-float"></div>
            <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-primary-400/20 rounded-full opacity-40 animate-float" style="animation-delay: 1s;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>